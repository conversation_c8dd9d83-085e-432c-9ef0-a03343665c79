// Seed sample data for testing
import { Pool } from 'pg';
import dotenv from 'dotenv';
import crypto from 'crypto';

// Simple UUID generator for older Node.js versions
function generateUUID() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c == 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

dotenv.config();

const pool = new Pool({
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '5432'),
  user: process.env.DB_USER || 'rasa_user',
  password: process.env.DB_PASSWORD || 'rasa_local_pass',
  database: process.env.DB_NAME || 'rasa_local_db',
  ssl: process.env.DB_SSL === 'true' ? true : false,
});

async function seedData() {
  const client = await pool.connect();
  
  try {
    console.log('🌱 Seeding sample data...');
    
    // Create a test user
    const userId = generateUUID();
    await client.query(`
      INSERT INTO users (id, email, password, first_name, last_name) 
      VALUES ($1, $2, $3, $4, $5)
      ON CONFLICT (email) DO NOTHING
    `, [userId, '<EMAIL>', 'hashed_password', 'Test', 'User']);
    
    console.log('✅ Test user created');
    
    // Create a test chat
    const chatResult = await client.query(`
      INSERT INTO chats (user_id, title) 
      VALUES ($1, $2) 
      RETURNING id
    `, [userId, 'Test Career Counseling Chat']);
    
    const chatId = chatResult.rows[0].id;
    console.log('✅ Test chat created');
    
    // Add some test messages
    await client.query(`
      INSERT INTO messages (chat_id, user_id, content, role) 
      VALUES 
        ($1, $2, 'Hola, me siento perdido en mi carrera profesional', 'user'),
        ($1, $2, 'El miedo al cambio de carrera es completamente normal. Es una decisión importante que merece ser tomada con cuidado.', 'assistant')
    `, [chatId, userId]);
    
    console.log('✅ Test messages created');
    
    // Add some psychology questions
    await client.query(`
      INSERT INTO psychology_questions (question, category, order_index) 
      VALUES 
        ('¿Cómo te sientes acerca de tu situación laboral actual?', 'initial_assessment', 1),
        ('¿Qué aspectos de tu trabajo te generan más estrés?', 'stress_assessment', 2),
        ('¿Tienes claridad sobre tus objetivos profesionales?', 'goal_assessment', 3)
      ON CONFLICT DO NOTHING
    `);
    
    console.log('✅ Psychology questions created');
    
    // Add some user context
    await client.query(`
      INSERT INTO user_context (user_id, context_key, context_value) 
      VALUES 
        ($1, 'career_stage', 'mid-career'),
        ($1, 'industry', 'technology'),
        ($1, 'main_concern', 'career_transition')
    `, [userId]);
    
    console.log('✅ User context created');
    
    console.log('🎉 Sample data seeded successfully!');
    
  } catch (error) {
    console.error('❌ Error seeding data:', error.message);
  } finally {
    client.release();
    await pool.end();
  }
}

seedData();
