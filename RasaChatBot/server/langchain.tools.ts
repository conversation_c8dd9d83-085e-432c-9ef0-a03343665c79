import { Chat<PERSON>penAI } from "@langchain/openai";
import { 
  DynamicTool,
  Tool
} from "@langchain/core/tools";
import { 
  CallbackManager
} from "@langchain/core/callbacks/manager";
import { getLangChainConfig } from './langchain.config';
import { storage } from './storage';

const config = getLangChainConfig();

// Initialize LLM for tools
const toolLLM = new ChatOpenAI({
  openAIApiKey: process.env.OPENAI_API_KEY,
  modelName: config.modelName,
  temperature: 0.1, // Lower temperature for tools
  streaming: false,
});

// Callback manager for logging
export const callbackManager = new CallbackManager();

// Tool for getting user context
export const getUserContextTool = new DynamicTool({
  name: "get_user_context",
  description: "Get stored user context information like name, age, location, preferences, etc.",
  func: async (userId: string) => {
    try {
      const userContexts = await storage.getAllUserContext(userId);
      if (userContexts.length === 0) {
        return "No user context information found.";
      }
      
      const contextString = userContexts
        .map(ctx => `${ctx.key}: ${ctx.value}`)
        .join(', ');
      
      return `User context: ${contextString}`;
    } catch (error) {
      console.error('Error getting user context:', error);
      return "Error retrieving user context.";
    }
  },
});

// Tool for setting user context
export const setUserContextTool = new DynamicTool({
  name: "set_user_context",
  description: "Store user context information like name, age, location, preferences, etc.",
  func: async (input: string) => {
    try {
      // Expected format: "userId:key:value"
      const [userId, key, value] = input.split(':');
      if (!userId || !key || !value) {
        return "Invalid format. Expected: userId:key:value";
      }
      
      await storage.setUserContext(userId, key, value);
      return `Successfully stored ${key}: ${value} for user ${userId}`;
    } catch (error) {
      console.error('Error setting user context:', error);
      return "Error storing user context.";
    }
  },
});

// Tool for searching similar conversations
export const searchSimilarConversationsTool = new DynamicTool({
  name: "search_similar_conversations",
  description: "Search for similar conversations based on user input to provide context-aware responses.",
  func: async (input: string) => {
    try {
      // Expected format: "userId:query:limit"
      const [userId, query, limitStr] = input.split(':');
      const limit = parseInt(limitStr) || 3;
      
      if (!userId || !query) {
        return "Invalid format. Expected: userId:query:limit";
      }
      
      const similarEmbeddings = await storage.getSimilarEmbeddings(userId, query, limit);
      if (similarEmbeddings.length === 0) {
        return "No similar conversations found.";
      }
      
      const contextSnippets = similarEmbeddings
        .map(s => `User: ${s.user_input}\nAssistant: ${s.bot_output}`)
        .join('\n\n');
      
      return `Similar conversations:\n${contextSnippets}`;
    } catch (error) {
      console.error('Error searching similar conversations:', error);
      return "Error searching similar conversations.";
    }
  },
});

// Tool for getting chat history
export const getChatHistoryTool = new DynamicTool({
  name: "get_chat_history",
  description: "Get the conversation history for a specific chat.",
  func: async (chatId: string) => {
    try {
      const chat = await storage.getChatWithMessages(parseInt(chatId));
      if (!chat) {
        return "Chat not found.";
      }
      
      const history = chat.messages
        .map(msg => `${msg.role}: ${msg.content}`)
        .join('\n');
      
      return `Chat history:\n${history}`;
    } catch (error) {
      console.error('Error getting chat history:', error);
      return "Error retrieving chat history.";
    }
  },
});

// Tool for generating summaries
export const generateSummaryTool = new DynamicTool({
  name: "generate_summary",
  description: "Generate a summary of the conversation or text content.",
  func: async (content: string) => {
    try {
      const summaryPrompt = `Genera un resumen breve y conciso del siguiente contenido en español:

${content}

Resumen:`;

      const response = await toolLLM.invoke(summaryPrompt);
      return response.content as string;
    } catch (error) {
      console.error('Error generating summary:', error);
      return "Error generating summary.";
    }
  },
});

// Tool for language detection and translation
export const languageTool = new DynamicTool({
  name: "language_detection",
  description: "Detect the language of the input text and provide translation if needed.",
  func: async (text: string) => {
    try {
      const languagePrompt = `Analiza el siguiente texto y responde en formato JSON:
        - Detecta el idioma
        - Si no es español, proporciona una traducción al español
        - Si es español, indica que no necesita traducción

        Texto: "${text}"

        Respuesta en formato JSON:`;

      const response = await toolLLM.invoke(languagePrompt);
      return response.content as string;
    } catch (error) {
      console.error('Error in language detection:', error);
      return "Error detecting language.";
    }
  },
});

// Tool for sentiment analysis
export const sentimentAnalysisTool = new DynamicTool({
  name: "sentiment_analysis",
  description: "Analyze the sentiment of the user's message to provide more empathetic responses.",
  func: async (text: string) => {
    try {
      const sentimentPrompt = `Analiza el sentimiento del siguiente texto y responde en formato JSON:
      - Determina si es positivo, negativo, neutral, o mixto
      - Identifica la emoción principal (alegría, tristeza, enojo, miedo, sorpresa, etc.)
      - Proporciona una puntuación de -1 (muy negativo) a 1 (muy positivo)

      Texto: "${text}"

      Respuesta en formato JSON:`;

      const response = await toolLLM.invoke(sentimentPrompt);
      return response.content as string;
    } catch (error) {
      console.error('Error in sentiment analysis:', error);
      return "Error analyzing sentiment.";
    }
  },
});

// Tool for session management
export const sessionManagementTool = new DynamicTool({
  name: "session_management",
  description: "Manage user sessions, track session numbers, and handle session transitions.",
  func: async (input: string) => {
    try {
      // Expected format: "userId:action:value"
      const [userId, action, value] = input.split(':');

      if (!userId || !action) {
        return "Invalid format. Expected: userId:action:value";
      }

      switch (action) {
        case 'increment_session':
          const currentSession = await storage.getUserContext(userId, 'sessionNumber');
          const newSessionNumber = (parseInt(currentSession || '0') + 1).toString();
          await storage.setUserContext(userId, 'sessionNumber', newSessionNumber);
          return `Session number incremented to ${newSessionNumber}`;

        case 'get_session_number':
          const sessionNumber = await storage.getUserContext(userId, 'sessionNumber');
          return sessionNumber || '1';

        case 'set_session_data':
          const [key, dataValue] = value.split('=');
          if (key && dataValue) {
            await storage.setUserContext(userId, key, dataValue);
            return `Session data ${key} set to ${dataValue}`;
          }
          return "Invalid session data format";

        default:
          return `Unknown action: ${action}`;
      }
    } catch (error) {
      console.error('Error in session management:', error);
      return "Error managing session.";
    }
  },
});

// Tool for extracting relationships from text
export const extractRelationshipsTool = new DynamicTool({
  name: "extract_relationships",
  description: "Extract and store relationship information from user messages.",
  func: async (input: string) => {
    try {
      // Expected format: "userId:message"
      const [userId, message] = input.split(':');

      if (!userId || !message) {
        return "Invalid format. Expected: userId:message";
      }

      const extractionPrompt = `Analiza el siguiente mensaje y extrae todas las relaciones familiares, de amistad o personales mencionadas.

      Mensaje: "${message}"

      Busca patrones como:
      - "Mi hermana María"
      - "Juan es mi padre"
      - "Vivo con mi esposa"
      - "Mi amigo Carlos"
      - "Trabajo con Ana"

      Responde en formato JSON con un array de relaciones:
      [
        {
          "entity1": "yo",
          "relationship": "hermana",
          "entity2": "María"
        }
      ]

      Si no encuentras relaciones, responde con un array vacío: []`;

      const response = await toolLLM.invoke(extractionPrompt);
      const content = response.content as string;

      try {
        // Extract JSON from response
        const jsonMatch = content.match(/\[[\s\S]*\]/);
        if (jsonMatch) {
          const relationships = JSON.parse(jsonMatch[0]);

          // Store each relationship in the database
          let storedCount = 0;
          for (const rel of relationships) {
            if (rel.entity1 && rel.relationship && rel.entity2) {
              await storage.addRelationship(
                userId,
                rel.entity1,
                rel.relationship,
                rel.entity2
              );
              storedCount++;
            }
          }

          return storedCount > 0
            ? `Extracted and stored ${storedCount} relationships`
            : "No relationships found in message";
        }
      } catch (parseError) {
        console.error('Error parsing relationship JSON:', parseError);
      }

      return "No relationships extracted";
    } catch (error) {
      console.error('Error extracting relationships:', error);
      return "Error extracting relationships.";
    }
  },
});

// Helper functions for relationship inference
function reverseRelationship(relationship: string): string {
  const reverseMap: Record<string, string> = {
    'padre': 'hijo',
    'madre': 'hija',
    'hijo': 'padre',
    'hija': 'madre',
    'hermano': 'hermana',
    'hermana': 'hermano',
    'esposo': 'esposa',
    'esposa': 'esposo',
    'abuelo': 'nieto',
    'abuela': 'nieta',
    'nieto': 'abuelo',
    'nieta': 'abuela',
    'tío': 'sobrino',
    'tía': 'sobrina',
    'sobrino': 'tío',
    'sobrina': 'tía'
  };

  return reverseMap[relationship.toLowerCase()] || relationship;
}

function inferThroughIntermediates(relationships: any[], entity1: string, entity2: string): string | null {
  // This is a simplified inference - could be expanded for more complex family trees
  // For now, just return null to indicate no inference possible
  return null;
}

// Tool for inferring relationships between entities
export const inferRelationshipTool = new DynamicTool({
  name: "infer_relationship",
  description: "Infer the relationship between two entities based on stored relationship data.",
  func: async (input: string) => {
    try {
      // Expected format: "userId:entity1:entity2"
      const [userId, entity1, entity2] = input.split(':');

      if (!userId || !entity1 || !entity2) {
        return "Invalid format. Expected: userId:entity1:entity2";
      }

      // Get all relationships for the user
      const relationships = await storage.getRelationships(userId);

      // Direct relationship lookup
      const directRelation = relationships.find(rel =>
        (rel.entity1.toLowerCase() === entity1.toLowerCase() && rel.entity2.toLowerCase() === entity2.toLowerCase()) ||
        (rel.entity1.toLowerCase() === entity2.toLowerCase() && rel.entity2.toLowerCase() === entity1.toLowerCase())
      );

      if (directRelation) {
        if (directRelation.entity1.toLowerCase() === entity1.toLowerCase()) {
          return `Relationship found: ${directRelation.relationship}`;
        } else {
          // Reverse relationship
          return `Relationship found: ${reverseRelationship(directRelation.relationship)}`;
        }
      }

      // Try to infer through intermediate relationships
      const inferredRelation = inferThroughIntermediates(relationships, entity1, entity2);
      if (inferredRelation) {
        return `Inferred relationship: ${inferredRelation}`;
      }

      return "No relationship found between the entities";
    } catch (error) {
      console.error('Error inferring relationship:', error);
      return "Error inferring relationship.";
    }
  },
});

// Export all tools
export const langChainTools: Tool[] = [
  getUserContextTool,
  setUserContextTool,
  searchSimilarConversationsTool,
  getChatHistoryTool,
  generateSummaryTool,
  languageTool,
  sentimentAnalysisTool,
  sessionManagementTool,
  extractRelationshipsTool,
  inferRelationshipTool,
];

// Tool executor utility
export class ToolExecutor {
  private tools: Map<string, Tool>;

  constructor() {
    this.tools = new Map();
    langChainTools.forEach(tool => {
      this.tools.set(tool.name, tool);
    });
  }

  async executeTool(toolName: string, input: string): Promise<string> {
    const tool = this.tools.get(toolName);
    if (!tool) {
      throw new Error(`Tool ${toolName} not found`);
    }
    
    try {
      return await tool.invoke(input);
    } catch (error) {
      console.error(`Error executing tool ${toolName}:`, error);
      throw error;
    }
  }

  getAvailableTools(): string[] {
    return Array.from(this.tools.keys());
  }

  getToolDescriptions(): Record<string, string> {
    const descriptions: Record<string, string> = {};
    this.tools.forEach((tool, name) => {
      descriptions[name] = tool.description;
    });
    return descriptions;
  }
}

export const toolExecutor = new ToolExecutor(); 