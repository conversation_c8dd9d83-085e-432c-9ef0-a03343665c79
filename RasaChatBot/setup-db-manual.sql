-- Manual PostgreSQL setup for <PERSON><PERSON> ChatBot
-- Run this as the postgres user: sudo -u postgres psql

-- Create user
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'rasa_user') THEN
        CREATE USER rasa_user WITH PASSWORD 'rasa_local_pass';
    END IF;
END
$$;

-- Create database
SELECT 'CREATE DATABASE rasa_local_db OWNER rasa_user'
WHERE NOT EXISTS (SELECT FROM pg_database WHERE datname = 'rasa_local_db')\gexec

-- Grant privileges
GRANT ALL PRIVILEGES ON DATABASE rasa_local_db TO rasa_user;
ALTER USER rasa_user CREATEDB;

-- Connect to the new database
\c rasa_local_db

-- Install pgvector extension (optional, for embeddings)
CREATE EXTENSION IF NOT EXISTS vector;

-- Show success message
SELECT 'Database setup completed successfully!' as status;
