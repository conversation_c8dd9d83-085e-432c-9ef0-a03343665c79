#!/bin/bash

echo "🚀 Setting up local PostgreSQL database for Rasa ChatBot..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Database configuration
DB_NAME="rasa_local_db"
DB_USER="rasa_user"
DB_PASSWORD="rasa_local_pass"
DB_HOST="localhost"
DB_PORT="5432"

echo -e "${YELLOW}Step 1: Creating PostgreSQL user and database...${NC}"

# Switch to postgres user and create database and user
sudo -u postgres psql << EOF
-- Create user if not exists
DO \$\$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = '$DB_USER') THEN
        CREATE USER $DB_USER WITH PASSWORD '$DB_PASSWORD';
    END IF;
END
\$\$;

-- Create database if not exists
SELECT 'CREATE DATABASE $DB_NAME OWNER $DB_USER'
WHERE NOT EXISTS (SELECT FROM pg_database WHERE datname = '$DB_NAME')\gexec

-- Grant privileges
GRANT ALL PRIVILEGES ON DATABASE $DB_NAME TO $DB_USER;
ALTER USER $DB_USER CREATEDB;

\q
EOF

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Database and user created successfully!${NC}"
else
    echo -e "${RED}❌ Failed to create database and user${NC}"
    exit 1
fi

echo -e "${YELLOW}Step 2: Testing database connection...${NC}"

# Test connection
PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "SELECT version();" > /dev/null 2>&1

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Database connection successful!${NC}"
else
    echo -e "${RED}❌ Database connection failed${NC}"
    exit 1
fi

echo -e "${YELLOW}Step 3: Installing pgvector extension...${NC}"

# Install pgvector extension for embeddings
sudo -u postgres psql -d $DB_NAME << EOF
CREATE EXTENSION IF NOT EXISTS vector;
\q
EOF

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ pgvector extension installed!${NC}"
else
    echo -e "${YELLOW}⚠️  pgvector extension installation failed (this is optional)${NC}"
fi

echo -e "${GREEN}🎉 Local PostgreSQL database setup complete!${NC}"
echo ""
echo -e "${YELLOW}Database Configuration:${NC}"
echo "  Host: $DB_HOST"
echo "  Port: $DB_PORT"
echo "  Database: $DB_NAME"
echo "  User: $DB_USER"
echo "  Password: $DB_PASSWORD"
echo ""
echo -e "${YELLOW}Next steps:${NC}"
echo "1. Update your db.ts and drizzle.config.ts files with the new configuration"
echo "2. Run 'npm run db:push' to create tables"
echo "3. Run 'npm run db:seed' if you have seed data"
