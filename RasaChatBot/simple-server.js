// Simple Node.js server compatible with older Node versions
import express from 'express';
import { Pool } from 'pg';
import path from 'path';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables
dotenv.config();

const app = express();
const port = process.env.PORT || 3001;

// Database connection
const pool = new Pool({
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '5432'),
  user: process.env.DB_USER || 'rasa_user',
  password: process.env.DB_PASSWORD || 'rasa_local_pass',
  database: process.env.DB_NAME || 'rasa_local_db',
  ssl: process.env.DB_SSL === 'true' ? true : false,
});

// Middleware
app.use(express.json());
app.use(express.static('client/dist'));

// Test database connection
pool.connect()
  .then(() => console.log('✅ Database connection successful'))
  .catch((err) => console.error('❌ Database connection failed:', err.message));

// API Routes
app.get('/api/health', (req, res) => {
  res.json({ status: 'OK', message: 'Server is running' });
});

app.get('/api/db-status', async (req, res) => {
  try {
    const client = await pool.connect();
    const result = await client.query('SELECT COUNT(*) FROM users');
    client.release();
    
    res.json({ 
      status: 'connected', 
      userCount: result.rows[0].count,
      database: process.env.DB_NAME || 'rasa_local_db'
    });
  } catch (error) {
    res.status(500).json({ 
      status: 'error', 
      message: error.message 
    });
  }
});

app.get('/api/users', async (req, res) => {
  try {
    const client = await pool.connect();
    const result = await client.query('SELECT id, email, first_name, last_name, created_at FROM users ORDER BY created_at DESC');
    client.release();
    
    res.json(result.rows);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.get('/api/chats', async (req, res) => {
  try {
    const client = await pool.connect();
    const result = await client.query(`
      SELECT c.id, c.title, c.created_at, u.email as user_email 
      FROM chats c 
      JOIN users u ON c.user_id = u.id 
      ORDER BY c.created_at DESC
    `);
    client.release();
    
    res.json(result.rows);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.get('/api/messages/:chatId', async (req, res) => {
  try {
    const { chatId } = req.params;
    const client = await pool.connect();
    const result = await client.query(`
      SELECT m.id, m.content, m.role, m.created_at, u.email as user_email
      FROM messages m 
      JOIN users u ON m.user_id = u.id 
      WHERE m.chat_id = $1 
      ORDER BY m.created_at ASC
    `, [chatId]);
    client.release();
    
    res.json(result.rows);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Serve React app for all other routes
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'client/dist/index.html'));
});

// Start server
app.listen(port, () => {
  console.log(`🚀 Server running on http://localhost:${port}`);
  console.log(`📊 Database: ${process.env.DB_NAME || 'rasa_local_db'}`);
  console.log(`🔗 Health check: http://localhost:${port}/api/health`);
  console.log(`📈 DB status: http://localhost:${port}/api/db-status`);
});

// Graceful shutdown
process.on('SIGINT', async () => {
  console.log('\n🛑 Shutting down server...');
  await pool.end();
  process.exit(0);
});
