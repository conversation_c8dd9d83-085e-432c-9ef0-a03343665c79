// Simple test to verify the psychology.ts fix
console.log('🧪 Testing Psychology Agent Fix...\n');

// Test that the problematic template is removed
const fs = require('fs');
const path = require('path');

try {
  const psychologyFile = fs.readFileSync(path.join(__dirname, 'server', 'psychology.ts'), 'utf8');
  
  // Check if the problematic hardcoded template is removed
  const problematicTemplate = `You are a helpful assistant.

Here is an example of how you should respond:

{
  "firstName": "<PERSON>",
  "lastName": "Doe",
  "age": 21
}`;

  if (psychologyFile.includes(problematicTemplate)) {
    console.log('❌ FAILED: Problematic template still exists in psychology.ts');
    console.log('The hardcoded template that causes LangChain variable errors is still present.');
    process.exit(1);
  } else {
    console.log('✅ SUCCESS: Problematic template has been removed from psychology.ts');
    console.log('The LangChain template variable error should now be fixed.');
  }

  // Check that the fix is in place
  if (psychologyFile.includes('// Use the original message directly - don\'t override it with template')) {
    console.log('✅ SUCCESS: Fix comment is present, indicating the correct fix was applied');
  } else {
    console.log('⚠️  WARNING: Fix comment not found, but template was removed');
  }

  console.log('\n🎉 Psychology Agent fix verification completed!');
  console.log('\n📋 What was fixed:');
  console.log('- ❌ Removed hardcoded message template with JSON examples');
  console.log('- ❌ Removed template variables that LangChain couldn\'t resolve');
  console.log('- ✅ Now uses original user message directly');
  console.log('- ✅ LangChain template variable error should be resolved');

} catch (error) {
  console.error('❌ Error reading psychology.ts file:', error.message);
  process.exit(1);
}
