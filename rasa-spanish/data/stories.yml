version: "3.1"

stories:
  - story: saludo y despedida simple
    steps:
      - intent: saludo
      - action: utter_saludo
      - intent: despedida
      - action: utter_despedida

  - story: pedir información general
    steps:
      - intent: saludo
      - action: utter_saludo
      - intent: pedir_informacion
      - action: utter_informacion_general
      - intent: pregunta_general
      - action: utter_pregunta_general
      - intent: despedida
      - action: utter_despedida

  - story: reporte de problema y feedback
    steps:
      - intent: reportar_problema
      - action: utter_agradecer_reporte
      - intent: dar_feedback
      - action: utter_agradecer_feedback
      - intent: despedida
      - action: utter_despedida

  - story: consultar estado de una solicitud
    steps:
      - intent: consultar_estado
      - action: utter_pedir_datos_estado
      - intent: afirmar
      - action: utter_confirmacion_positiva
      - action: action_verificar_estado
      - action: utter_estado_actual
      - intent: despedida
      - action: utter_despedida

  - story: chitchat casual
    steps:
      - intent: chitchat
      - action: utter_respuesta_chitchat
      - intent: despedida
      - action: utter_despedida

  - story: manejo de afirmaciones y negaciones
    steps:
      - intent: pedir_informacion
      - action: utter_pedir_confirmacion
      - intent: afirmar
      - action: utter_confirmacion_positiva
      - intent: negar
      - action: utter_confirmacion_negativa
      - intent: despedida
      - action: utter_despedida

  - story: ayuda y repetición
    steps:
      - intent: pedir_ayuda
      - action: utter_ofrecer_ayuda
      - intent: pedir_repeticion
      - action: utter_repetir_ultimo
      - intent: despedida
      - action: utter_despedida

  - story: hacer reserva exitosa
    steps:
      - intent: hacer_reserva
      - action: utter_pedir_detalles_reserva
      - intent: afirmar
      - action: utter_confirmacion_positiva
      - action: action_realizar_reserva
      - action: utter_confirmar_reserva
      - intent: despedida
      - action: utter_despedida

  - story: cancelar reserva
    steps:
      - intent: cancelar_reserva
      - action: utter_pedir_confirmacion_cancelacion
      - intent: afirmar
      - action: utter_confirmacion_positiva
      - action: action_cancelar_reserva
      - action: utter_confirmar_cancelacion
      - intent: despedida
      - action: utter_despedida

  - story: manejo out_of_scope y fallback
    steps:
      - intent: out_of_scope
      - action: utter_no_entendido
      - intent: nlu_fallback
      - action: utter_fallback
      - intent: despedida
      - action: utter_despedida
